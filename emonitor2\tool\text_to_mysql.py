#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
テキストファイルからデータを読み込み、MySQLデータベースに登録するプログラム
"""

import os
import sys
import logging
import mysql.connector
from mysql.connector import Error
from config import DB_CONFIG

# ログ設定
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


def connect_to_database():
    """
    MySQLデータベースへの接続を確立する
    
    Returns:
        connection: 接続オブジェクト
    """
    try:
        connection = mysql.connector.connect(
            host=DB_CONFIG['host'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            database=DB_CONFIG['database'],
            port=DB_CONFIG['port']
        )
        if connection.is_connected():
            logger.info("MySQLデータベースに接続しました")
            return connection
    except Error as e:
        logger.error(f"MySQLへの接続エラー: {e}")
        return None


def read_text_file(file_path):
    """
    テキストファイルからデータを読み込む
    
    Args:
        file_path (str): テキストファイルのパス
        
    Returns:
        list: 読み込んだデータのリスト
    """
    data_list = []
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            for line in file:
                # 行末の改行を削除してカンマで分割
                row_data = line.strip().split(',')
                if len(row_data) >= 4:  # 必要なデータ項目（ID、名前、年齢、メール）があるか確認
                    data_list.append(row_data)
        logger.info(f"{len(data_list)}件のデータを読み込みました")
        return data_list
    except Exception as e:
        logger.error(f"ファイル読み込みエラー: {e}")
        return []


def insert_data_to_mysql(connection, data_list):
    """
    データをMySQLデータベースに挿入する
    
    Args:
        connection: データベース接続オブジェクト
        data_list (list): 挿入するデータのリスト
        
    Returns:
        int: 挿入に成功したデータの件数
    """
    cursor = None
    success_count = 0
    
    try:
        cursor = connection.cursor()
        
        # データ挿入用のSQL
        insert_query = """
        INSERT INTO users (user_id, name, age, email)
        VALUES (%s, %s, %s, %s)
        """
        
        for data in data_list:
            try:
                # データの型を適切に変換
                user_id = int(data[0])
                name = data[1]
                age = int(data[2])
                email = data[3]
                
                # データ挿入実行
                cursor.execute(insert_query, (user_id, name, age, email))
                success_count += 1
                
            except Exception as e:
                logger.error(f"データ挿入エラー: {e}, データ: {data}")
                continue
        
        # トランザクションのコミット
        connection.commit()
        logger.info(f"{success_count}件のデータを正常に挿入しました")
        
        return success_count
    except Error as e:
        logger.error(f"SQLエラー: {e}")
        # エラーが発生した場合はロールバック
        if connection.is_connected():
            connection.rollback()
        return 0
    finally:
        # カーソルを閉じる
        if cursor is not None:
            cursor.close()


def main(file_path):
    """
    メイン処理
    
    Args:
        file_path (str): テキストファイルのパス
    """
    # ファイルの存在確認
    if not os.path.exists(file_path):
        logger.error(f"ファイルが存在しません: {file_path}")
        return
    
    # データベース接続
    connection = connect_to_database()
    if connection is None:
        return
    
    try:
        # テキストファイルからデータを読み込む
        data_list = read_text_file(file_path)
        
        if data_list:
            # データをMySQLに挿入
            insert_data_to_mysql(connection, data_list)
        else:
            logger.warning("挿入するデータがありません")
            
    except Exception as e:
        logger.error(f"処理中にエラーが発生しました: {e}")
    finally:
        # データベース接続を閉じる
        if connection.is_connected():
            connection.close()
            logger.info("データベース接続を閉じました")


if __name__ == "__main__":
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
    else:
        # デフォルトファイルパス
        file_path = "sample_data.txt"
    
    logger.info(f"処理開始: ファイル {file_path}")
    main(file_path)
    logger.info("処理終了")
