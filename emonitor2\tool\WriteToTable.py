#!/usr/bin/env python
# -*- coding: utf-8 -*-

# PythonでMySQLを使用するために必要なライブラリ
import shutil
import datetime
import glob
import os
#from time import time
import pymysql.cursors

py_filename = os.path.basename(__file__)
py_dirname = os.path.dirname(__file__)

if os.name == 'nt':
    search_filepath = py_dirname + '\\消費電力\\*.txt'
else:
    search_filepath = '/home/<USER>/Visual Studio Code/消費電力/*.txt'

# データベースにアクセスするためのパラメータ 
connection = pymysql.connect(host='localhost',
    user='root',
    password='',
    db='SmartMater',
    charset='utf8',
    # Selectの結果をdictionary形式で受け取る
    cursorclass=pymysql.cursors.DictCursor)

today = datetime.datetime.today()

def writeTable(file):
    print(file)

    filename = os.path.basename(file)
    if os.path.splitext(filename)[0] == today.strftime('%Y%m%d') or os.path.splitext(filename)[0] == today.strftime('%Y%m%d') + 'A':
        print('skip')                
        return

    with open(file, 'r') as f:
        num = 0
        while f.readable():
            line = f.readline()
            line = line.strip()
            if line == "":
                break

            values = line.replace('\0', '').split(":")

            if 'A' in filename:
                if values.__len__() == 2:
                    day = datetime.datetime(int(filename[0:4]),
                        int(filename[4:6]), int(filename[6:8]))
                    time = day + datetime.timedelta(minutes=int(values[0])*30)
                    value = values[1].replace('\n', '')
                    sql = "INSERT INTO Per30Minute (DateTime, Value) VALUES (%s, %s)"
                    cur.execute(sql, (time, value))
                    num += 1
            else:
                if values.__len__() == 4:
                    time = values[0] + ':' + values[1] + ':' + values[2]
                    value = values[3].replace('\n', '')
                    sql = "INSERT INTO PerMinute (DateTime, Value) VALUES (%s, %s)"
                    cur.execute(sql, (time, value))
                    num += 1

    print(num)

    # コミットしてトランザクション実行
    connection.commit()

    if os.name == 'nt':
        newpath = py_dirname + '\\消費電力\\backup\\' + filename
    else:
        newpath = '/home/<USER>/Visual Studio Code/消費電力/backup/' + filename
    shutil.move(file, newpath)

with connection:
    # カーソルを取得する
    with connection.cursor() as cur:
        files = glob.glob(search_filepath)
        for file in files :
            writeTable(file)
        cur.close
    connection.close
