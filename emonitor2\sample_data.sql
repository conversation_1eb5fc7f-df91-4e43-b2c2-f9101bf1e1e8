-- サンプルデータ挿入用SQLファイル
-- テスト用の電力データを生成します

-- データベース作成（必要に応じて）
CREATE DATABASE IF NOT EXISTS smartmater CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE smartmater;

-- テーブル作成
CREATE TABLE IF NOT EXISTS perminute (
    id INT AUTO_INCREMENT PRIMARY KEY,
    datetime DATETIME NOT NULL,
    value DECIMAL(10,2) NOT NULL,
    INDEX idx_datetime (datetime)
);

CREATE TABLE IF NOT EXISTS per30minute (
    id INT AUTO_INCREMENT PRIMARY KEY,
    datetime DATETIME NOT NULL,
    value DECIMAL(10,4) NOT NULL,
    INDEX idx_datetime (datetime)
);

-- サンプルデータ挿入（今日の日付）
-- 瞬間使用電力データ（1時間ごと）
INSERT INTO perminute (datetime, value) VALUES
(CONCAT(CURDATE(), ' 00:00:00'), 800.5),
(CONCAT(CURDATE(), ' 01:00:00'), 750.2),
(CONCAT(CURDATE(), ' 02:00:00'), 720.8),
(CONCAT(CURDATE(), ' 03:00:00'), 700.1),
(CONCAT(CURDATE(), ' 04:00:00'), 680.5),
(CONCAT(CURDATE(), ' 05:00:00'), 690.3),
(CONCAT(CURDATE(), ' 06:00:00'), 850.7),
(CONCAT(CURDATE(), ' 07:00:00'), 1200.4),
(CONCAT(CURDATE(), ' 08:00:00'), 1450.8),
(CONCAT(CURDATE(), ' 09:00:00'), 1380.2),
(CONCAT(CURDATE(), ' 10:00:00'), 1320.6),
(CONCAT(CURDATE(), ' 11:00:00'), 1280.9),
(CONCAT(CURDATE(), ' 12:00:00'), 1500.3),
(CONCAT(CURDATE(), ' 13:00:00'), 1420.7),
(CONCAT(CURDATE(), ' 14:00:00'), 1350.1),
(CONCAT(CURDATE(), ' 15:00:00'), 1280.5),
(CONCAT(CURDATE(), ' 16:00:00'), 1180.8),
(CONCAT(CURDATE(), ' 17:00:00'), 1250.2),
(CONCAT(CURDATE(), ' 18:00:00'), 1680.6),
(CONCAT(CURDATE(), ' 19:00:00'), 1850.9),
(CONCAT(CURDATE(), ' 20:00:00'), 1720.3),
(CONCAT(CURDATE(), ' 21:00:00'), 1580.7),
(CONCAT(CURDATE(), ' 22:00:00'), 1320.1),
(CONCAT(CURDATE(), ' 23:00:00'), 980.5);

-- 30分積算電力データ（30分ごと）
INSERT INTO per30minute (datetime, value) VALUES
(CONCAT(CURDATE(), ' 00:30:00'), 0.4025),
(CONCAT(CURDATE(), ' 01:00:00'), 0.3751),
(CONCAT(CURDATE(), ' 01:30:00'), 0.3604),
(CONCAT(CURDATE(), ' 02:00:00'), 0.3501),
(CONCAT(CURDATE(), ' 02:30:00'), 0.3403),
(CONCAT(CURDATE(), ' 03:00:00'), 0.3452),
(CONCAT(CURDATE(), ' 03:30:00'), 0.4254),
(CONCAT(CURDATE(), ' 04:00:00'), 0.6002),
(CONCAT(CURDATE(), ' 04:30:00'), 0.7254),
(CONCAT(CURDATE(), ' 05:00:00'), 0.6901),
(CONCAT(CURDATE(), ' 05:30:00'), 0.6603),
(CONCAT(CURDATE(), ' 06:00:00'), 0.6405),
(CONCAT(CURDATE(), ' 06:30:00'), 0.7502),
(CONCAT(CURDATE(), ' 07:00:00'), 0.7104),
(CONCAT(CURDATE(), ' 07:30:00'), 0.6751),
(CONCAT(CURDATE(), ' 08:00:00'), 0.6403),
(CONCAT(CURDATE(), ' 08:30:00'), 0.5904),
(CONCAT(CURDATE(), ' 09:00:00'), 0.6251),
(CONCAT(CURDATE(), ' 09:30:00'), 0.8403),
(CONCAT(CURDATE(), ' 10:00:00'), 0.9255),
(CONCAT(CURDATE(), ' 10:30:00'), 0.8602),
(CONCAT(CURDATE(), ' 11:00:00'), 0.7904),
(CONCAT(CURDATE(), ' 11:30:00'), 0.6601),
(CONCAT(CURDATE(), ' 12:00:00'), 0.4903);

-- 過去1週間のサンプルデータ
INSERT INTO per30minute (datetime, value) VALUES
(DATE_SUB(CURDATE(), INTERVAL 1 DAY), 25.5),
(DATE_SUB(CURDATE(), INTERVAL 2 DAY), 28.2),
(DATE_SUB(CURDATE(), INTERVAL 3 DAY), 26.8),
(DATE_SUB(CURDATE(), INTERVAL 4 DAY), 24.1),
(DATE_SUB(CURDATE(), INTERVAL 5 DAY), 27.3),
(DATE_SUB(CURDATE(), INTERVAL 6 DAY), 29.7),
(DATE_SUB(CURDATE(), INTERVAL 7 DAY), 23.9);
