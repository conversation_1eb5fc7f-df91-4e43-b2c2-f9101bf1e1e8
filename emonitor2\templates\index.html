{% extends "base.html" %}

{% block title %}ホーム - 電力監視システム{% endblock %}

{% block content %}
<div class="hero-section">
    <div class="hero-content">
        <h1 class="hero-title">電力監視システム</h1>
        <p class="hero-description">リアルタイムで電力使用量を監視し、効率的なエネルギー管理を実現します</p>
        
        <div class="feature-cards">
            <div class="feature-card">
                <div class="feature-icon">📊</div>
                <h3>日別グラフ</h3>
                <p>日付を指定して詳細な電力使用量を確認</p>
                <a href="{{ url_for('daily') }}" class="btn btn-primary">表示する</a>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">📈</div>
                <h3>月別グラフ</h3>
                <p>月間の電力使用量推移を分析</p>
                <a href="{{ url_for('monthly') }}" class="btn btn-primary">表示する</a>
            </div>
        </div>
    </div>
</div>

<div class="stats-section">
    <div class="stats-container">
        <h2>システム概要</h2>
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-value" id="current-power">--</div>
                <div class="stat-label">現在の使用電力 (W)</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="today-total">--</div>
                <div class="stat-label">本日の使用電力量 (kWh)</div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 現在の日付を取得
const today = new Date().toISOString().split('T')[0];

// 本日のデータを取得して表示
fetch(`/api/daily/${today}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 本日の総使用電力量を表示
            document.getElementById('today-total').textContent = data.daily_total.toFixed(2);
            
            // 最新の瞬間使用電力を表示
            if (data.instantaneous_power.length > 0) {
                const latestPower = data.instantaneous_power[data.instantaneous_power.length - 1];
                document.getElementById('current-power').textContent = latestPower.value;
            }
        }
    })
    .catch(error => {
        console.error('データ取得エラー:', error);
    });
</script>
{% endblock %}
