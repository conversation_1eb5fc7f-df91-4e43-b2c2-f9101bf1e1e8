# テキストファイルからMySQLデータベースにデータを登録するプログラム

このプログラムは、テキストファイルからデータを読み込み、MySQLデータベースに登録するPythonスクリプトです。

## 前提条件

- Python 3.6以上
- MySQLサーバー

## インストール方法

1. 必要なライブラリをインストールします。

```bash
pip install -r requirements.txt
```

2. MySQLサーバーで`create_table.sql`を実行し、必要なデータベースとテーブルを作成します。

```bash
mysql -u root -p < create_table.sql
```

3. `config.py`ファイルのデータベース接続情報を、実際の環境に合わせて修正してください。

## 使用方法

```bash
python text_to_mysql.py [テキストファイルのパス]
```

テキストファイルのパスを指定しない場合は、デフォルトで`sample_data.txt`が使用されます。

## テキストファイルの形式

テキストファイルは、カンマ区切りで以下の形式である必要があります。

```
ユーザーID,名前,年齢,メールアドレス
```

例:
```
1,田中太郎,28,<EMAIL>
2,佐藤花子,35,<EMAIL>
```

## 機能

- テキストファイルからデータを読み込む
- MySQLデータベースへの接続
- データの挿入処理
- エラーハンドリングとロギング
