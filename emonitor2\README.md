# 電力監視システム (emonitor2)

使用電力量をリアルタイムで監視し、グラフィカルに表示するWebアプリケーションです。

## 機能

- **日別グラフ**: 指定した日の瞬間使用電力と30分積算電力をグラフ表示
- **月別グラフ**: 指定した月の日別使用電力量をグラフ表示
- **リアルタイム監視**: 現在の使用電力と本日の総使用電力量を表示
- **レスポンシブデザイン**: モバイルデバイスにも対応

## 技術スタック

- **バックエンド**: Python Flask
- **データベース**: MySQL
- **フロントエンド**: HTML5, CSS3, JavaScript
- **グラフライブラリ**: Chart.js

## セットアップ

### 1. 依存関係のインストール

```bash
pip install -r requirements.txt
```

### 2. 環境変数の設定

`.env.example`をコピーして`.env`ファイルを作成し、データベース接続情報を設定してください。

```bash
cp .env.example .env
```

`.env`ファイルを編集:
```
DB_HOST=localhost
DB_USER=your_username
DB_PASSWORD=your_password
DB_NAME=emonitor
```

### 3. データベースの準備

MySQLデータベースを作成し、以下のAPIエンドポイントでテーブルを初期化してください:

```
GET /api/init
```

### 4. アプリケーションの起動

```bash
python app.py
```

アプリケーションは `http://localhost:5000` で起動します。

## データベーススキーマ

### instantaneous_power テーブル
- `id`: 主キー
- `timestamp`: 測定日時
- `power_w`: 瞬間使用電力 (W)

### accumulated_power テーブル
- `id`: 主キー
- `timestamp`: 測定日時
- `accumulated_kwh`: 30分積算電力量 (kWh)

## API エンドポイント

### 日別データ取得
```
GET /api/daily/<date>
```
指定した日付（YYYY-MM-DD形式）の電力データを取得

### 月別データ取得
```
GET /api/monthly/<year>/<month>
```
指定した年月の日別電力データを取得

### データベース初期化
```
GET /api/init
```
データベーステーブルを作成

## ディレクトリ構造

```
emonitor2/
├── app.py              # メインアプリケーション
├── models.py           # データベースモデル
├── requirements.txt    # Python依存関係
├── .env.example       # 環境変数テンプレート
├── templates/         # HTMLテンプレート
│   ├── base.html
│   ├── index.html
│   ├── daily.html
│   └── monthly.html
└── static/           # 静的ファイル
    ├── css/
    │   └── style.css
    └── js/
        ├── main.js
        ├── daily.js
        └── monthly.js
```

## 使用方法

1. **ホームページ**: 現在の使用電力と本日の総使用電力量を確認
2. **日別グラフ**: 日付を選択して詳細な電力使用量を確認
3. **月別グラフ**: 年月を選択して月間の電力使用量推移を確認

## 開発者向け情報

### データの追加

電力データは以下のようにMySQLに直接挿入できます:

```sql
-- 瞬間使用電力データ
INSERT INTO instantaneous_power (timestamp, power_w) VALUES 
('2024-01-01 10:00:00', 1500.5);

-- 30分積算電力データ
INSERT INTO accumulated_power (timestamp, accumulated_kwh) VALUES 
('2024-01-01 10:30:00', 0.75);
```

### カスタマイズ

- CSS: `static/css/style.css`でスタイルをカスタマイズ
- JavaScript: `static/js/`内のファイルで機能を拡張
- チャート設定: Chart.jsの設定を変更してグラフの見た目を調整

## ライセンス

MIT License
