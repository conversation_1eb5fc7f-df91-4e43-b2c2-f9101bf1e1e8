{% extends "base.html" %}

{% block title %}日別グラフ - 電力監視システム{% endblock %}

{% block content %}
<div class="page-header">
    <h1>日別電力使用量グラフ</h1>
    <div class="date-selector">
        <label for="date-input">日付を選択:</label>
        <input type="date" id="date-input" class="date-input">
        <button id="load-data-btn" class="btn btn-primary">データ読み込み</button>
    </div>
</div>

<div class="chart-container">
    <div class="chart-tabs">
        <button class="tab-btn active" data-chart="instantaneous">瞬間使用電力</button>
        <button class="tab-btn" data-chart="accumulated">30分積算電力</button>
    </div>
    
    <div class="chart-wrapper">
        <canvas id="power-chart"></canvas>
    </div>
    
    <div class="chart-info">
        <div class="info-item">
            <span class="info-label">選択日:</span>
            <span id="selected-date">--</span>
        </div>
        <div class="info-item">
            <span class="info-label">総使用電力量:</span>
            <span id="total-power">-- kWh</span>
        </div>
        <div class="info-item">
            <span class="info-label">最大使用電力:</span>
            <span id="max-power">-- W</span>
        </div>
    </div>
</div>

<div class="loading-overlay" id="loading-overlay">
    <div class="loading-spinner"></div>
    <p>データを読み込み中...</p>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/daily.js') }}"></script>
{% endblock %}
