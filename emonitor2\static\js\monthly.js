// 月別グラフページのJavaScript

let monthlyChart = null;
let currentMonthlyData = null;

// ページ読み込み時の初期化
document.addEventListener('DOMContentLoaded', function() {
    // 年選択肢を生成
    populateYearOptions();
    
    // 現在の年月を設定
    const now = new Date();
    document.getElementById('year-input').value = now.getFullYear();
    document.getElementById('month-input').value = now.getMonth() + 1;
    
    // イベントリスナー設定
    document.getElementById('load-monthly-btn').addEventListener('click', loadMonthlyData);
    document.getElementById('year-input').addEventListener('change', loadMonthlyData);
    document.getElementById('month-input').addEventListener('change', loadMonthlyData);
    
    // 初期データ読み込み
    loadMonthlyData();
});

// 年選択肢を生成
function populateYearOptions() {
    const yearSelect = document.getElementById('year-input');
    const currentYear = new Date().getFullYear();
    
    // 過去5年から未来2年まで
    for (let year = currentYear - 5; year <= currentYear + 2; year++) {
        const option = document.createElement('option');
        option.value = year;
        option.textContent = `${year}年`;
        if (year === currentYear) {
            option.selected = true;
        }
        yearSelect.appendChild(option);
    }
}

// 月別データ読み込み
async function loadMonthlyData() {
    const year = document.getElementById('year-input').value;
    const month = document.getElementById('month-input').value;
    
    if (!year || !month) {
        utils.showError('年月を選択してください');
        return;
    }
    
    utils.showLoading();
    
    try {
        const response = await fetch(`/api/monthly/${year}/${month}`);
        const data = await response.json();
        
        if (data.success) {
            currentMonthlyData = data;
            updateMonthlyStats(data);
            createMonthlyChart(data);
        } else {
            utils.showError(data.error || 'データの取得に失敗しました');
        }
    } catch (error) {
        console.error('データ取得エラー:', error);
        utils.showError('データの取得に失敗しました');
    } finally {
        utils.hideLoading();
    }
}

// 月別統計更新
function updateMonthlyStats(data) {
    const monthNames = ['1月', '2月', '3月', '4月', '5月', '6月', 
                       '7月', '8月', '9月', '10月', '11月', '12月'];
    
    document.getElementById('selected-month').textContent = 
        `${data.year}年${monthNames[data.month - 1]}`;
    
    document.getElementById('monthly-total').textContent = 
        `${utils.formatNumber(data.monthly_total)} kWh`;
    
    // 日平均計算
    const dailyAverage = data.daily_data.length > 0 ? 
        data.monthly_total / data.daily_data.length : 0;
    document.getElementById('daily-average').textContent = 
        `${utils.formatNumber(dailyAverage)} kWh`;
    
    // 最大日使用電力量
    const maxDaily = data.daily_data.length > 0 ? 
        Math.max(...data.daily_data.map(item => parseFloat(item.total_kwh))) : 0;
    document.getElementById('max-daily').textContent = 
        `${utils.formatNumber(maxDaily)} kWh`;
}

// 月別チャート作成
function createMonthlyChart(data) {
    const ctx = document.getElementById('monthly-chart').getContext('2d');
    
    // 既存のチャートを破棄
    if (monthlyChart) {
        monthlyChart.destroy();
    }
    
    // チャートデータ準備
    const chartData = {
        labels: data.daily_data.map(item => {
            const date = new Date(item.date);
            return date.getDate();
        }),
        datasets: [{
            label: '日別使用電力量 (kWh)',
            data: data.daily_data.map(item => parseFloat(item.total_kwh)),
            backgroundColor: 'rgba(102, 126, 234, 0.8)',
            borderColor: 'rgb(102, 126, 234)',
            borderWidth: 2,
            borderRadius: 4,
            borderSkipped: false,
        }]
    };
    
    // チャートオプション
    const chartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            title: {
                display: true,
                text: `${data.year}年${data.month}月 日別使用電力量`,
                font: {
                    size: 16,
                    weight: 'bold'
                }
            },
            legend: {
                display: false
            },
            tooltip: {
                callbacks: {
                    title: function(context) {
                        const dataIndex = context[0].dataIndex;
                        const date = new Date(data.daily_data[dataIndex].date);
                        return `${date.getMonth() + 1}月${date.getDate()}日`;
                    },
                    label: function(context) {
                        return `使用電力量: ${utils.formatNumber(context.parsed.y)} kWh`;
                    }
                }
            }
        },
        scales: {
            x: {
                title: {
                    display: true,
                    text: '日'
                },
                grid: {
                    display: false
                }
            },
            y: {
                title: {
                    display: true,
                    text: '使用電力量 (kWh)'
                },
                beginAtZero: true,
                grid: {
                    color: 'rgba(0, 0, 0, 0.1)'
                }
            }
        },
        interaction: {
            intersect: false,
            mode: 'index'
        },
        animation: {
            duration: 1000,
            easing: 'easeInOutQuart'
        }
    };
    
    monthlyChart = new Chart(ctx, {
        type: 'bar',
        data: chartData,
        options: chartOptions
    });
}
