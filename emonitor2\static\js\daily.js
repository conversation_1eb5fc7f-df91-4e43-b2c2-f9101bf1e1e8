// 日別グラフページのJavaScript

let powerChart = null;
let currentData = null;
let currentChartType = 'instantaneous';

// ページ読み込み時の初期化
document.addEventListener('DOMContentLoaded', function() {
    // 今日の日付を設定
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('date-input').value = today;
    
    // イベントリスナー設定
    document.getElementById('load-data-btn').addEventListener('click', loadDailyData);
    document.getElementById('date-input').addEventListener('change', loadDailyData);
    
    // タブ切り替え
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const chartType = this.dataset.chart;
            switchChart(chartType);
        });
    });
    
    // 初期データ読み込み
    loadDailyData();
});

// 日別データ読み込み
async function loadDailyData() {
    const dateInput = document.getElementById('date-input');
    const selectedDate = dateInput.value;
    
    if (!selectedDate) {
        utils.showError('日付を選択してください');
        return;
    }
    
    utils.showLoading();
    
    try {
        const response = await fetch(`/api/daily/${selectedDate}`);
        const data = await response.json();
        
        if (data.success) {
            currentData = data;
            updateChartInfo(data);
            createChart(currentChartType);
        } else {
            utils.showError(data.error || 'データの取得に失敗しました');
        }
    } catch (error) {
        console.error('データ取得エラー:', error);
        utils.showError('データの取得に失敗しました');
    } finally {
        utils.hideLoading();
    }
}

// チャート情報更新
function updateChartInfo(data) {
    document.getElementById('selected-date').textContent = utils.formatDate(data.date);
    document.getElementById('total-power').textContent = `${utils.formatNumber(data.daily_total)} kWh`;
    
    // 最大使用電力計算
    let maxPower = 0;
    if (data.instantaneous_power.length > 0) {
        maxPower = Math.max(...data.instantaneous_power.map(item => parseFloat(item.power_w)));
    }
    document.getElementById('max-power').textContent = `${utils.formatNumber(maxPower, 0)} W`;
}

// チャート作成
function createChart(chartType) {
    const ctx = document.getElementById('power-chart').getContext('2d');
    
    // 既存のチャートを破棄
    if (powerChart) {
        powerChart.destroy();
    }
    
    let chartData, chartOptions;
    
    if (chartType === 'instantaneous') {
        chartData = createInstantaneousChartData();
        chartOptions = createInstantaneousChartOptions();
    } else {
        chartData = createAccumulatedChartData();
        chartOptions = createAccumulatedChartOptions();
    }
    
    powerChart = new Chart(ctx, {
        type: 'line',
        data: chartData,
        options: chartOptions
    });
}

// 瞬間使用電力チャートデータ作成
function createInstantaneousChartData() {
    const data = currentData.instantaneous_power.map(item => ({
        x: new Date(item.timestamp),
        y: parseFloat(item.power_w)
    }));
    
    return {
        datasets: [{
            label: '瞬間使用電力 (W)',
            data: data,
            borderColor: 'rgb(102, 126, 234)',
            backgroundColor: 'rgba(102, 126, 234, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4
        }]
    };
}

// 30分積算電力チャートデータ作成
function createAccumulatedChartData() {
    const data = currentData.accumulated_power.map(item => ({
        x: new Date(item.timestamp),
        y: parseFloat(item.accumulated_kwh)
    }));
    
    return {
        datasets: [{
            label: '30分積算電力 (kWh)',
            data: data,
            borderColor: 'rgb(118, 75, 162)',
            backgroundColor: 'rgba(118, 75, 162, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4
        }]
    };
}

// 瞬間使用電力チャートオプション
function createInstantaneousChartOptions() {
    return {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            title: {
                display: true,
                text: '瞬間使用電力の推移',
                font: {
                    size: 16,
                    weight: 'bold'
                }
            },
            legend: {
                display: false
            }
        },
        scales: {
            x: {
                type: 'time',
                time: {
                    displayFormats: {
                        hour: 'HH:mm'
                    }
                },
                title: {
                    display: true,
                    text: '時刻'
                }
            },
            y: {
                title: {
                    display: true,
                    text: '使用電力 (W)'
                },
                beginAtZero: true
            }
        },
        interaction: {
            intersect: false,
            mode: 'index'
        }
    };
}

// 30分積算電力チャートオプション
function createAccumulatedChartOptions() {
    return {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            title: {
                display: true,
                text: '30分積算電力の推移',
                font: {
                    size: 16,
                    weight: 'bold'
                }
            },
            legend: {
                display: false
            }
        },
        scales: {
            x: {
                type: 'time',
                time: {
                    displayFormats: {
                        hour: 'HH:mm'
                    }
                },
                title: {
                    display: true,
                    text: '時刻'
                }
            },
            y: {
                title: {
                    display: true,
                    text: '積算電力 (kWh)'
                },
                beginAtZero: true
            }
        },
        interaction: {
            intersect: false,
            mode: 'index'
        }
    };
}

// チャート切り替え
function switchChart(chartType) {
    currentChartType = chartType;
    
    // タブの状態更新
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-chart="${chartType}"]`).classList.add('active');
    
    // チャート再作成
    if (currentData) {
        createChart(chartType);
    }
}
