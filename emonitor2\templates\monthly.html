{% extends "base.html" %}

{% block title %}月別グラフ - 電力監視システム{% endblock %}

{% block content %}
<div class="page-header">
    <h1>月別電力使用量グラフ</h1>
    <div class="month-selector">
        <label for="year-input">年:</label>
        <select id="year-input" class="year-input"></select>
        
        <label for="month-input">月:</label>
        <select id="month-input" class="month-input">
            <option value="1">1月</option>
            <option value="2">2月</option>
            <option value="3">3月</option>
            <option value="4">4月</option>
            <option value="5">5月</option>
            <option value="6">6月</option>
            <option value="7">7月</option>
            <option value="8">8月</option>
            <option value="9">9月</option>
            <option value="10">10月</option>
            <option value="11">11月</option>
            <option value="12">12月</option>
        </select>
        
        <button id="load-monthly-btn" class="btn btn-primary">データ読み込み</button>
    </div>
</div>

<div class="chart-container">
    <div class="chart-wrapper">
        <canvas id="monthly-chart"></canvas>
    </div>
    
    <div class="monthly-stats">
        <div class="stat-card">
            <h3>月間統計</h3>
            <div class="stat-grid">
                <div class="stat-item">
                    <span class="stat-label">選択月:</span>
                    <span id="selected-month">--</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">月間総使用電力量:</span>
                    <span id="monthly-total">-- kWh</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">日平均使用電力量:</span>
                    <span id="daily-average">-- kWh</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">最大日使用電力量:</span>
                    <span id="max-daily">-- kWh</span>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="loading-overlay" id="loading-overlay">
    <div class="loading-spinner"></div>
    <p>データを読み込み中...</p>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/monthly.js') }}"></script>
{% endblock %}
