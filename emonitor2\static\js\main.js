// メインJavaScriptファイル

// ユーティリティ関数
const utils = {
    // 日付フォーマット
    formatDate: (date) => {
        return new Date(date).toLocaleDateString('ja-<PERSON>');
    },
    
    // 時刻フォーマット
    formatTime: (datetime) => {
        return new Date(datetime).toLocaleTimeString('ja-<PERSON>', {
            hour: '2-digit',
            minute: '2-digit'
        });
    },
    
    // 数値フォーマット
    formatNumber: (num, decimals = 2) => {
        return parseFloat(num).toFixed(decimals);
    },
    
    // ローディング表示
    showLoading: () => {
        document.getElementById('loading-overlay').classList.add('show');
    },
    
    // ローディング非表示
    hideLoading: () => {
        document.getElementById('loading-overlay').classList.remove('show');
    },
    
    // エラー表示
    showError: (message) => {
        alert(`エラー: ${message}`);
    }
};

// Chart.jsのデフォルト設定
Chart.defaults.font.family = "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif";
Chart.defaults.color = '#666';

// グローバル変数
window.utils = utils;
