import mysql.connector
from datetime import datetime, timedelta
import os
from dotenv import load_dotenv

load_dotenv()

# データベース接続設定
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', ''),
    'database': os.getenv('DB_NAME', 'emonitor'),
    'charset': 'utf8mb4'
}

class PowerData:
    """電力データモデル"""
    
    @staticmethod
    def get_db_connection():
        """データベース接続を取得"""
        try:
            connection = mysql.connector.connect(**DB_CONFIG)
            return connection
        except mysql.connector.Error as err:
            print(f"データベース接続エラー: {err}")
            return None

    @staticmethod
    def get_instantaneous_power(date_str):
        """指定日の瞬間使用電力データを取得"""
        connection = PowerData.get_db_connection()
        if not connection:
            return []
        
        try:
            cursor = connection.cursor(dictionary=True)
            query = """
                SELECT datetime, value 
                FROM perminute 
                WHERE DATE(datetime) = %s 
                ORDER BY datetime
            """
            cursor.execute(query, (date_str,))
            result = cursor.fetchall()
            return result
        except mysql.connector.Error as err:
            print(f"データ取得エラー: {err}")
            return []
        finally:
            cursor.close()
            connection.close()

    @staticmethod
    def get_accumulated_power(date_str):
        """指定日の30分積算電力データを取得"""
        connection = PowerData.get_db_connection()
        if not connection:
            return []
        
        try:
            cursor = connection.cursor(dictionary=True)
            query = """
                SELECT datetime, value 
                FROM per30minute 
                WHERE DATE(datetime) = %s 
                ORDER BY datetime
            """
            cursor.execute(query, (date_str,))
            result = cursor.fetchall()
            print(result)
            return result
        except mysql.connector.Error as err:
            print(f"データ取得エラー: {err}")
            return []
        finally:
            cursor.close()
            connection.close()

    @staticmethod
    def get_daily_total(date_str):
        """指定日の総使用電力量を計算"""
        connection = PowerData.get_db_connection()
        if not connection:
            return 0
        
        try:
            cursor = connection.cursor()
            query = """
                SELECT SUM(value) as total_kwh
                FROM per30minute
                WHERE DATE(datetime) = %s
            """
            cursor.execute(query, (date_str,))
            result = cursor.fetchone()
            return result[0] if result[0] else 0
        except mysql.connector.Error as err:
            print(f"データ取得エラー: {err}")
            return 0
        finally:
            cursor.close()
            connection.close()

    @staticmethod
    def get_monthly_data(year, month):
        """指定月の日別使用電力量データを取得"""
        connection = PowerData.get_db_connection()
        if not connection:
            return []
        
        try:
            cursor = connection.cursor(dictionary=True)
            query = """
                SELECT DATE(datetime) as date, SUM(value) as total_kwh
                FROM per30minute
                WHERE YEAR(datetime) = %s AND MONTH(datetime) = %s
                GROUP BY DATE(datetime)
                ORDER BY date
            """
            cursor.execute(query, (year, month))
            result = cursor.fetchall()
            return result
        except mysql.connector.Error as err:
            print(f"データ取得エラー: {err}")
            return []
        finally:
            cursor.close()
            connection.close()

    @staticmethod
    def create_tables():
        """テーブル作成（初期化用）"""
        connection = PowerData.get_db_connection()
        if not connection:
            return False
        
        try:
            cursor = connection.cursor()
            
            # 瞬間使用電力テーブル
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS perminute (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    datetime DATETIME NOT NULL,
                    value DECIMAL(10,2) NOT NULL,
                    INDEX idx_datetime (datetime)
                )
            """)

            # 30分積算電力テーブル
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS per30minute (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    datetime DATETIME NOT NULL,
                    value DECIMAL(10,4) NOT NULL,
                    INDEX idx_datetime (datetime)
                )
            """)
            
            connection.commit()
            return True
        except mysql.connector.Error as err:
            print(f"テーブル作成エラー: {err}")
            return False
        finally:
            cursor.close()
            connection.close()
