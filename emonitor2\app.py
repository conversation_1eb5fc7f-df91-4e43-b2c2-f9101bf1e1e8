from flask import Flask, render_template, jsonify, request
from flask_cors import CORS
import mysql.connector
from datetime import datetime, timedelta
import os
from dotenv import load_dotenv

# 環境変数を読み込み
load_dotenv()

app = Flask(__name__)
CORS(app)

# データベース接続設定
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', ''),
    'database': os.getenv('DB_NAME', 'emonitor'),
    'charset': 'utf8mb4'
}

def get_db_connection():
    """データベース接続を取得"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        return connection
    except mysql.connector.Error as err:
        print(f"データベース接続エラー: {err}")
        return None

@app.route('/')
def index():
    """メインページ"""
    return render_template('index.html')

@app.route('/daily')
def daily():
    """日別グラフページ"""
    return render_template('daily.html')

@app.route('/monthly')
def monthly():
    """月別グラフページ"""
    return render_template('monthly.html')

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
