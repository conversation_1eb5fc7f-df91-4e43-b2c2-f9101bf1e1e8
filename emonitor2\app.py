from flask import Flask, render_template, jsonify, request
from flask_cors import CORS
from datetime import datetime, timedelta
import os
from dotenv import load_dotenv
from models import PowerData

# 環境変数を読み込み
load_dotenv()

app = Flask(__name__)
CORS(app)

@app.route('/')
def index():
    """メインページ"""
    return render_template('index.html')

@app.route('/daily')
def daily():
    """日別グラフページ"""
    return render_template('daily.html')

@app.route('/monthly')
def monthly():
    """月別グラフページ"""
    return render_template('monthly.html')

# API エンドポイント
@app.route('/api/daily/<date_str>')
def api_daily_data(date_str):
    """日別データAPI"""
    try:
        # 日付形式の検証
        datetime.strptime(date_str, '%Y-%m-%d')

        # データベース接続テスト
        connection = PowerData.get_db_connection()
        if not connection:
            return jsonify({'success': False, 'error': 'データベースに接続できません'}), 500
        connection.close()

        # 瞬間使用電力データ取得
        instantaneous_data = PowerData.get_instantaneous_power(date_str)

        # 30分積算電力データ取得
        accumulated_data = PowerData.get_accumulated_power(date_str)

        # 日別総使用電力量取得
        daily_total = PowerData.get_daily_total(date_str)

        return jsonify({
            'success': True,
            'date': date_str,
            'instantaneous_power': instantaneous_data,
            'accumulated_power': accumulated_data,
            'daily_total': float(daily_total)
        })
    except ValueError:
        return jsonify({'success': False, 'error': '無効な日付形式です'}), 400
    except Exception as e:
        return jsonify({'success': False, 'error': f'サーバーエラー: {str(e)}'}), 500

@app.route('/api/monthly/<int:year>/<int:month>')
def api_monthly_data(year, month):
    """月別データAPI"""
    try:
        # 月別データ取得
        monthly_data = PowerData.get_monthly_data(year, month)

        # 月間総使用電力量計算
        monthly_total = sum(float(day['total_kwh']) for day in monthly_data)

        return jsonify({
            'success': True,
            'year': year,
            'month': month,
            'daily_data': monthly_data,
            'monthly_total': monthly_total
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

#@app.route('/api/init')
def api_init_db():
    """データベース初期化API"""
    try:
        success = PowerData.create_tables()
        if success:
            return jsonify({'success': True, 'message': 'データベースが初期化されました'})
        else:
            return jsonify({'success': False, 'error': 'データベース初期化に失敗しました'}), 500
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
