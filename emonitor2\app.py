from flask import Flask, render_template, request, jsonify, redirect, url_for
import os

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'  # 本番環境では環境変数から取得してください

# ホームページ
@app.route('/')
def index():
    return render_template('index.html')

# About ページ
@app.route('/about')
def about():
    return render_template('about.html')

# API エンドポイント例
@app.route('/api/data')
def api_data():
    sample_data = {
        'message': 'Hello from Flask API!',
        'status': 'success',
        'data': [
            {'id': 1, 'name': 'Item 1'},
            {'id': 2, 'name': 'Item 2'},
            {'id': 3, 'name': 'Item 3'}
        ]
    }
    return jsonify(sample_data)

# POST リクエストを処理するエンドポイント
@app.route('/api/submit', methods=['POST'])
def submit_data():
    data = request.get_json()
    # ここでデータを処理します
    response = {
        'message': 'Data received successfully',
        'received_data': data
    }
    return jsonify(response)

# エラーハンドラー
@app.errorhandler(404)
def not_found(error):
    return render_template('404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    return render_template('500.html'), 500

if __name__ == '__main__':
    # デバッグモードで実行（本番環境では False にしてください）
    app.run(debug=True, host='0.0.0.0', port=5000)
